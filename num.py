#!/usr/bin/env python3
"""
phone_checker_app.py: Um app simples em Python para validar e extrair informações de números de telefone.
"""
import argparse
import phonenumbers
from phonenumbers import geocoder, carrier, timezone

def check_number(number: str, default_region: str = None) -> dict:
    try:
        parsed = phonenumbers.parse(number, default_region)
        valid = phonenumbers.is_valid_number(parsed)
        possible = phonenumbers.is_possible_number(parsed)
        international = phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL)
        national = phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.NATIONAL)
        region = geocoder.description_for_number(parsed, "pt")
        service = carrier.name_for_number(parsed, "pt")
        zones = timezone.time_zones_for_number(parsed)
        return {
            "valid": valid,
            "possible": possible,
            "international": international,
            "national": national,
            "region": region,
            "carrier": service,
            "time_zones": zones
        }
    except phonenumbers.NumberParseException as e:
        return {"error": str(e)}

def main():
    parser = argparse.ArgumentParser(
        description="Valida e extrai informações de um número de telefone."
    )
    parser.add_argument(
        "number",
        help="Número de telefone a ser verificado (ex: +5511999998888 ou 11999998888)."
    )
    parser.add_argument(
        "-r", "--region",
        default=None,
        help="Código de região/país padrão (ex: BR, US)."
    )
    args = parser.parse_args()

    result = check_number(args.number, args.region)
    if "error" in result:
        print(f"Erro ao analisar número: {result['error']}")
    else:
        print(f"Número Internacional: {result['international']}")
        print(f"Número Nacional:     {result['national']}")
        print(f"Válido:             {result['valid']}")
        print(f"Possível:           {result['possible']}")
        print(f"Região:             {result['region']}")
        print(f"Operadora:          {result['carrier']}")
        print(f"Time Zones:         {', '.join(result['time_zones'])}")

if __name__ == "__main__":
    main()

# Requisitos:
# pip install phonenumbers
